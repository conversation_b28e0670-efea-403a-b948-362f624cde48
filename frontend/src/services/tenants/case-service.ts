import type { SupabaseClient } from '@supabase/supabase-js';
import {
  mapCaseFromDb,
  mapCasesFromDb,
  mapCaseToDb,
  mapCaseUpdateToDb,
  mapCaseClientsToDb
} from '../../mappers/tenants/case';
import type { Case, CaseInput, CaseStatus } from '../../types/domain/tenants/Case';
import type { Database } from '../../lib/supabase/database.types';
import { execSafeQuery, execCountQuery, execRpcQuery } from '../../lib/supabase/utils';

// Query parameters for case service
export interface CaseQueryParams {
  page?: number;
  limit?: number;
  status?: CaseStatus;
  practiceArea?: string;
  clientId?: string;
  searchTerm?: string;
}

export interface CaseQueryResult {
  cases: Case[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Type for document and deadline counts
// Simple interfaces to avoid TypeScript type depth issues
interface DocumentCount {
  case_id: string;
  count: number;
}

// Using any for certain response types to avoid TypeScript depth issues
type CountResponse = { count: number | null };

/**
 * Service for managing cases
 * All database interactions are encapsulated here and proper tenant isolation is enforced
 */
export class CaseService {
  constructor(
    private supabase: SupabaseClient<Database>,
    private tenantId: string
  ) {
    if (!tenantId) {
      throw new Error('Tenant ID is required for CaseService');
    }
  }

  /**
   * Get all cases with optional filtering and pagination
   */
  async getAll(params: CaseQueryParams = {}): Promise<CaseQueryResult> {
    const {
      page = 1,
      limit = 20,
      status,
      practiceArea,
      clientId,
      searchTerm
    } = params;

    // Calculate pagination parameters
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // First, get the total count for pagination info
    const { count } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId);

    // Build the base query
    let query = this.supabase
      .schema('tenants')
      .from('cases')
      .select(`
        *,
        primary_attorney:primary_attorney_id(*),
        clients:case_clients(client_id(*))
      `)
      .eq('tenant_id', this.tenantId)
      .order('created_at', { ascending: false })
      .range(from, to);

    // Apply filters if provided
    if (status) {
      query = query.eq('status', status);
    }

    if (practiceArea) {
      query = query.eq('practice_area', practiceArea);
    }

    if (clientId) {
      // Special handling for client filter - need to query through the case_clients table
      const { data: matterIds } = await this.supabase
        .schema('tenants')
        .from('case_clients')
        .select('matter_id')
        .eq('tenant_id', this.tenantId)
        .eq('client_id', clientId);

      if (matterIds && matterIds.length > 0) {
        const ids = matterIds.map(row => row.matter_id);
        query = query.in('id', ids);
      } else {
        // No matching cases, return empty result
        return {
          cases: [],
          totalCount: 0,
          page,
          limit,
          totalPages: 0
        };
      }
    }

    // Apply search term if provided
    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching cases:', error);
      throw new Error(`Failed to fetch cases: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return {
        cases: [],
        totalCount: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      };
    }

    // Calculate additional counts for each case
    const caseIds = data.map(c => c.id);

    // Create count promises for documents and deadlines using our RPC functions
    let documentCounts: DocumentCount[] = [];
    let deadlineCounts: DocumentCount[] = [];

    if (caseIds.length > 0) {
      try {
        // Create promises for all document counts
        const docCountPromises = caseIds.map(async (caseId) => {
          // Use RPC with proper type handling
          const count = await execRpcQuery<number>(() =>
            (this.supabase as any).rpc('count_documents_for_case', {
              p_case_id: caseId,
              p_tenant_id: this.tenantId
            })
          );
          return { case_id: caseId, count: count || 0 };
        });

        // Create promises for all deadline counts
        const dlCountPromises = caseIds.map(async (caseId) => {
          // Use RPC with proper type handling
          const count = await execRpcQuery<number>(() =>
            (this.supabase as any).rpc('count_deadlines_for_case', {
              p_case_id: caseId,
              p_tenant_id: this.tenantId
            })
          );
          return { case_id: caseId, count: count || 0 };
        });

        // Wait for all counts to complete
        [documentCounts, deadlineCounts] = await Promise.all([
          Promise.all(docCountPromises),
          Promise.all(dlCountPromises)
        ]);
      } catch (err) {
        console.warn('Error counting case related items:', err);
        // Fallback to zero counts if there was an error
        documentCounts = caseIds.map(caseId => ({ case_id: caseId, count: 0 }));
        deadlineCounts = caseIds.map(caseId => ({ case_id: caseId, count: 0 }));
      }
    }

    // Enrich the cases with counts
    const enrichedData = data.map(caseRow => {
      const countData = {
        document_count: documentCounts.find(d => d.case_id === caseRow.id)?.count ?? 0,
        deadline_count: deadlineCounts.find(d => d.case_id === caseRow.id)?.count ?? 0,
        note_count: 0 // Would add note count logic if there's a notes table
      };

      return {
        ...caseRow,
        ...countData
      };
    });

    // Calculate pagination info
    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      cases: mapCasesFromDb(enrichedData),
      totalCount,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Get a case by ID with all related data
   */
  async getById(id: string): Promise<Case | null> {
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select(`
        *,
        primary_attorney:primary_attorney_id(*),
        clients:case_clients(client_id(*))
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Record not found
        return null;
      }
      console.error('Error fetching case:', error);
      throw new Error(`Failed to fetch case: ${error.message}`);
    }

    // Get document and deadline counts using our RPC functions
    let documentCount = 0;
    let deadlineCount = 0;

    try {
      // Execute both counts in parallel for efficiency
      [documentCount, deadlineCount] = await Promise.all([
        // Count documents using RPC
        execRpcQuery<number>(() =>
          (this.supabase as any).rpc('count_documents_for_case', {
            p_case_id: id,
            p_tenant_id: this.tenantId
          })
        ).then(count => count || 0),

        // Count deadlines using RPC
        execRpcQuery<number>(() =>
          (this.supabase as any).rpc('count_deadlines_for_case', {
            p_case_id: id,
            p_tenant_id: this.tenantId
          })
        ).then(count => count || 0)
      ]);
    } catch (err) {
      console.warn('Error counting case documents or deadlines', err);
      // Keep the default counts of 0 if there was an error
    }

    // Enrich the case with counts
    const enrichedData = {
      ...data,
      document_count: documentCount,
      deadline_count: deadlineCount,
      note_count: 0 // Would add note count logic if there's a notes table
    };

    return mapCaseFromDb(enrichedData);
  }

  /**
   * Create a new case with associated clients
   */
  async create(userId: string, input: CaseInput, primaryClientId: string): Promise<Case> {
    // Ensure we have valid client IDs
    const clientIds = input.clientIds || [primaryClientId];
    if (!clientIds.includes(primaryClientId)) {
      clientIds.push(primaryClientId);
    }

    // Manual transaction to create case and associate clients
    // Insert case first
    const caseData = mapCaseToDb(input, userId, this.tenantId, primaryClientId);
    const { data: insertedCase, error: caseError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .insert(caseData)
      .select('*')
      .single();

    if (caseError) {
      console.error('Error creating case:', caseError);
      throw new Error(`Failed to create case: ${caseError.message}`);
    }

    // Insert case-client associations
    const caseClientData = mapCaseClientsToDb(insertedCase.id, clientIds, this.tenantId);
    const { error: clientError } = await this.supabase
      .schema('tenants')
      .from('case_clients')
      .insert(caseClientData);

    if (clientError) {
      console.error('Error associating clients with case:', clientError);

      // Try to clean up the case if client association fails
      try {
        await this.supabase
          .schema('tenants')
          .from('cases')
          .delete()
          .eq('id', insertedCase.id)
          .eq('tenant_id', this.tenantId);
      } catch (err) {
        console.error('Failed to clean up case after client association error', err);
      }

      throw new Error(`Failed to associate clients with case: ${clientError.message}`);
    }

    // Fetch the complete case with relations
    const createdCase = await this.getById(insertedCase.id);
    if (!createdCase) {
      throw new Error('Failed to retrieve newly created case');
    }

    return createdCase;
  }

  /**
   * Update a case
   */
  async update(id: string, userId: string, input: Partial<CaseInput>): Promise<Case> {
    // First get the original case to update its metadata correctly
    const { data: originalCase, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      console.error('Error fetching case for update:', fetchError);
      throw new Error(`Failed to fetch case for update: ${fetchError.message}`);
    }

    // Map the updates
    const updateData = mapCaseUpdateToDb(input, userId, originalCase);

    // Update the case
    const { error } = await this.supabase
      .schema('tenants')
      .from('cases')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error updating case:', error);
      throw new Error(`Failed to update case: ${error.message}`);
    }

    // Handle client associations if clientIds is provided
    if (input.clientIds && input.clientIds.length > 0) {
      // First delete existing associations
      const { error: deleteError } = await this.supabase
        .schema('tenants')
        .from('case_clients')
        .delete()
        .eq('case_id', id)
        .eq('tenant_id', this.tenantId);

      if (deleteError) {
        console.error('Error removing case-client associations:', deleteError);
        throw new Error(`Failed to update case-client associations: ${deleteError.message}`);
      }

      // Then insert new associations
      const caseClientData = mapCaseClientsToDb(id, input.clientIds, this.tenantId);
      const { error: insertError } = await this.supabase
        .schema('tenants')
        .from('case_clients')
        .insert(caseClientData);

      if (insertError) {
        console.error('Error creating case-client associations:', insertError);
        throw new Error(`Failed to update case-client associations: ${insertError.message}`);
      }
    }

    // Return the updated case
    const updatedCase = await this.getById(id);
    if (!updatedCase) {
      throw new Error('Failed to retrieve updated case');
    }

    return updatedCase;
  }

  /**
   * Delete a case
   * This will fail if there are related records that depend on this case
   */
  async delete(id: string): Promise<void> {
    // First, delete any case_clients associations
    try {
      const { error: clientError } = await this.supabase
        .schema('tenants')
        .from('case_clients')
        .delete()
        .eq('case_id', id)
        .eq('tenant_id', this.tenantId);

      if (clientError) {
        console.error('Error deleting case-client associations:', clientError);
        throw new Error(`Failed to delete case-client associations: ${clientError.message}`);
      }
    } catch (err) {
      console.error('Error deleting case-client associations:', err);
      throw err;
    }

    // Then delete the case itself
    try {
      const { error } = await this.supabase
        .schema('tenants')
        .from('cases')
        .delete()
        .eq('id', id)
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error('Error deleting case:', error);
        throw new Error(`Failed to delete case: ${error.message}`);
      }
    } catch (err) {
      console.error('Error deleting case:', err);
      throw err;
    }
  }

  /**
   * Get case statistics
   */
  async getStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPracticeArea: Record<string, number>;
  }> {
    // Get total count using our safe query helper
    const total = await execCountQuery(() =>
      this.supabase
        .schema('tenants')
        .from('cases')
        .select('count', { count: 'exact', head: true })
        .eq('tenant_id', this.tenantId)
    );

    // Prepare the result objects
    const byStatus: Record<string, number> = {};
    const byPracticeArea: Record<string, number> = {};

    try {
      // Use our RPC function for status counts with type assertion to bypass TypeScript limitations
      type StatusCount = Array<{status: string; count: number}>;
      const statusResult = await execRpcQuery<StatusCount>(() =>
        // We need to cast as any to bypass the TypeScript constraint with the RPC function name
        (this.supabase as any).rpc('count_cases_by_status', {
          p_tenant_id: this.tenantId
        })
      );

      // Use our RPC function for practice area counts with type assertion
      type PracticeAreaCount = Array<{practice_area: string; count: number}>;
      const practiceAreaResult = await execRpcQuery<PracticeAreaCount>(() =>
        // We need to cast as any to bypass the TypeScript constraint with the RPC function name
        (this.supabase as any).rpc('count_cases_by_practice_area', {
          p_tenant_id: this.tenantId
        })
      );

      // Process status results
      if (statusResult) {
        for (const item of statusResult) {
          byStatus[item.status || 'unknown'] = item.count || 0;
        }
      }

      // Process practice area results
      if (practiceAreaResult) {
        for (const item of practiceAreaResult) {
          byPracticeArea[item.practice_area || 'unknown'] = item.count || 0;
        }
      }
    } catch (err) {
      console.error('Error getting case statistics:', err);
    }

    return {
      total: total || 0,
      byStatus,
      byPracticeArea
    };
  }

  /**
   * Change the status of a case
   */
  async changeStatus(id: string, status: CaseStatus, userId: string): Promise<Case> {
    // Update the case status
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    // If case is closed, set closed_date
    if (status === 'closed') {
      updateData.closed_date = new Date().toISOString();
    }

    // Perform the update
    const { error } = await this.supabase
      .schema('tenants')
      .from('cases')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error updating case status:', error);
      throw new Error(`Failed to update case status: ${error.message}`);
    }

    // Return the updated case
    const updatedCase = await this.getById(id);
    if (!updatedCase) {
      throw new Error('Failed to retrieve updated case');
    }

    return updatedCase;
  }
}
